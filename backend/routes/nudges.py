from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from typing import List, Optional
from datetime import datetime
import uuid
import logging

from models.nudge import Nudge, NudgeCreate, NudgeUpdate, NudgeStatus, Medium
from firebase.init import get_firestore_client

router = APIRouter()

async def deliver_nudge(nudge: Nudge):
    """Background task to deliver nudge via selected medium"""
    try:
        logging.info(f"Delivering nudge {nudge.nudge_id} via {nudge.medium}")
        
        # Implement actual delivery logic based on medium
        if nudge.medium == Medium.EMAIL:
            # TODO: Implement email sending
            logging.info(f"Sending email to MSME {nudge.msme_id}: {nudge.message}")
            
        elif nudge.medium == Medium.WHATSAPP:
            # TODO: Implement WhatsApp sending
            logging.info(f"Sending WhatsApp to MSME {nudge.msme_id}: {nudge.message}")
            
        elif nudge.medium == Medium.SMS:
            # TODO: Implement SMS sending
            logging.info(f"Sending SMS to MSME {nudge.msme_id}: {nudge.message}")
            
        elif nudge.medium == Medium.PUSH_NOTIFICATION:
            # TODO: Implement push notification
            logging.info(f"Sending push notification to MSME {nudge.msme_id}: {nudge.message}")
        
        # Update nudge status to DELIVERED in Firestore
        db = get_firestore_client()
        nudge_ref = db.collection('msmes').document(nudge.msme_id).collection('nudges').document(nudge.nudge_id)
        nudge_ref.update({"status": NudgeStatus.DELIVERED})
        
    except Exception as e:
        logging.error(f"Failed to deliver nudge {nudge.nudge_id}: {str(e)}")
        # Update nudge status to FAILED in Firestore
        db = get_firestore_client()
        nudge_ref = db.collection('msmes').document(nudge.msme_id).collection('nudges').document(nudge.nudge_id)
        nudge_ref.update({"status": NudgeStatus.FAILED})

@router.post("/send", response_model=Nudge, status_code=status.HTTP_201_CREATED)
async def send_nudge(nudge_data: NudgeCreate, background_tasks: BackgroundTasks):
    """Send a nudge to an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(nudge_data.msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Generate unique nudge ID
        nudge_id = str(uuid.uuid4())
        
        # Create nudge
        nudge = Nudge(
            nudge_id=nudge_id,
            msme_id=nudge_data.msme_id,
            trigger_type=nudge_data.trigger_type,
            message=nudge_data.message,
            medium=nudge_data.medium,
            sent_at=datetime.utcnow(),
            status=NudgeStatus.PENDING,  # Initially set as pending
            metadata=nudge_data.metadata
        )
        
        # Save nudge to Firestore
        nudge_ref = db.collection('msmes').document(nudge_data.msme_id).collection('nudges').document(nudge_id)
        nudge_ref.set(nudge.dict())
        
        # Add delivery task to background tasks
        background_tasks.add_task(deliver_nudge, nudge)
        
        # Update status to SENT
        nudge.status = NudgeStatus.SENT
        nudge_ref.update({"status": NudgeStatus.SENT})
        
        return nudge
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send nudge: {str(e)}"
        )

@router.get("/{msme_id}", response_model=List[Nudge])
async def get_nudges(msme_id: str, status_filter: Optional[str] = None, limit: int = 50):
    """Get nudges for an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Build query
        query = db.collection('msmes').document(msme_id).collection('nudges')
        
        if status_filter:
            query = query.where('status', '==', status_filter)
            
        # Order by sent_at in descending order
        query = query.order_by('sent_at', direction='DESCENDING').limit(limit)
        
        # Execute query
        nudge_docs = query.stream()
        
        # Convert to Nudge models
        nudges = []
        for doc in nudge_docs:
            nudge_data = doc.to_dict()
            nudges.append(Nudge(**nudge_data))
            
        return nudges
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve nudges: {str(e)}"
        )

@router.put("/{msme_id}/{nudge_id}", response_model=Nudge)
async def update_nudge(msme_id: str, nudge_id: str, update_data: NudgeUpdate):
    """Update nudge status or content"""
    try:
        db = get_firestore_client()
        
        nudge_ref = db.collection('msmes').document(msme_id).collection('nudges').document(nudge_id)
        nudge_doc = nudge_ref.get()
        
        if not nudge_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nudge not found"
            )
        
        # Update only provided fields
        update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
        nudge_ref.update(update_dict)
        
        # Return updated nudge
        updated_doc = nudge_ref.get()
        data = updated_doc.to_dict()
        return Nudge(**data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update nudge: {str(e)}"
        )
