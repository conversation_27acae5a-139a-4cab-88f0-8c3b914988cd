from fastapi import APIRouter, HTTPException, status
from typing import List, Dict, Any
from datetime import datetime, timedelta

from firebase.init import get_firestore_client
from services.health_score import calculate_health_score

router = APIRouter()

@router.get("/portfolio", response_model=List[Dict[str, Any]])
async def get_portfolio_summary():
    """Get portfolio summary with all MSMEs, scores, and risk status"""
    try:
        db = get_firestore_client()
        
        # Get all MSMEs
        msmes_ref = db.collection('msmes')
        msmes_docs = msmes_ref.stream()
        
        portfolio = []
        
        for msme_doc in msmes_docs:
            msme_data = msme_doc.to_dict()
            msme_id = msme_doc.id
            
            # Get latest signals for this MSME
            signals_ref = db.collection('msmes').document(msme_id).collection('signals')
            signals_docs = signals_ref.order_by('timestamp', direction='DESCENDING').limit(10).stream()
            
            signals = []
            for signal_doc in signals_docs:
                signals.append(signal_doc.to_dict())
            
            # Use stored score if available, otherwise calculate
            stored_score = msme_data.get('score', 0)
            stored_risk_band = msme_data.get('risk_band', 'red')

            if stored_score > 0:
                current_score = stored_score
                risk_band = stored_risk_band
            else:
                # Fallback to calculation if no stored score
                score_details = calculate_health_score(signals)
                current_score = score_details['score']
                risk_band = score_details['risk_band']

            # Get recent nudges count
            nudges_ref = db.collection('msmes').document(msme_id).collection('nudges')
            recent_nudges = nudges_ref.where(
                'sent_at', '>=', (datetime.utcnow() - timedelta(days=7)).isoformat()
            ).stream()

            recent_nudges_count = len(list(recent_nudges))

            # Build portfolio entry
            portfolio_entry = {
                'msme_id': msme_id,
                'name': msme_data.get('name', 'Unknown'),
                'business_type': msme_data.get('business_type', 'unknown'),
                'location': msme_data.get('location', 'Unknown'),
                'current_score': current_score,
                'risk_band': risk_band,
                'score_trend': _calculate_score_trend(signals),
                'signals_count': len(signals),
                'recent_nudges': recent_nudges_count,
                'last_signal_date': signals[0].get('timestamp') if signals else None,
                'created_at': msme_data.get('created_at'),
                'tags': msme_data.get('tags', [])
            }
            
            portfolio.append(portfolio_entry)
        
        # Sort by risk band (red first) and then by score
        risk_order = {'red': 0, 'yellow': 1, 'green': 2}
        portfolio.sort(key=lambda x: (risk_order.get(x['risk_band'], 3), -x['current_score']))
        
        return portfolio
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get portfolio summary: {str(e)}"
        )

def _calculate_score_trend(signals: List[Dict[str, Any]]) -> str:
    """Calculate score trend based on recent signals"""
    if len(signals) < 2:
        return "stable"
    
    # Get scores from last two signal periods
    recent_signals = signals[:5]  # Last 5 signals
    older_signals = signals[5:10] if len(signals) > 5 else []
    
    if not older_signals:
        return "stable"
    
    recent_score = calculate_health_score(recent_signals)['score']
    older_score = calculate_health_score(older_signals)['score']
    
    score_diff = recent_score - older_score
    
    if score_diff > 5:
        return "improving"
    elif score_diff < -5:
        return "declining"
    else:
        return "stable"

@router.get("/analytics", response_model=Dict[str, Any])
async def get_dashboard_analytics():
    """Get dashboard analytics and summary statistics"""
    try:
        db = get_firestore_client()
        
        # Get all MSMEs
        msmes_ref = db.collection('msmes')
        msmes_docs = list(msmes_ref.stream())
        
        total_msmes = len(msmes_docs)
        risk_distribution = {'green': 0, 'yellow': 0, 'red': 0}
        business_type_distribution = {}
        total_signals = 0
        
        for msme_doc in msmes_docs:
            msme_data = msme_doc.to_dict()
            msme_id = msme_doc.id

            # Get signals count
            signals_ref = db.collection('msmes').document(msme_id).collection('signals')
            signals_count = len(list(signals_ref.stream()))
            total_signals += signals_count

            # Use stored risk band if available, otherwise calculate
            stored_score = msme_data.get('score', 0)
            stored_risk_band = msme_data.get('risk_band', 'red')

            if stored_score > 0:
                risk_band = stored_risk_band
            else:
                # Fallback to calculation if no stored score
                signals_docs = signals_ref.order_by('timestamp', direction='DESCENDING').limit(10).stream()
                signals = [doc.to_dict() for doc in signals_docs]
                score_details = calculate_health_score(signals)
                risk_band = score_details['risk_band']

            # Update risk distribution
            risk_distribution[risk_band] = risk_distribution.get(risk_band, 0) + 1

            # Update business type distribution
            business_type = msme_data.get('business_type', 'unknown')
            business_type_distribution[business_type] = business_type_distribution.get(business_type, 0) + 1
        
        return {
            'total_msmes': total_msmes,
            'total_signals': total_signals,
            'risk_distribution': risk_distribution,
            'business_type_distribution': business_type_distribution,
            'average_signals_per_msme': round(total_signals / total_msmes, 2) if total_msmes > 0 else 0,
            'last_updated': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get dashboard analytics: {str(e)}"
        )
