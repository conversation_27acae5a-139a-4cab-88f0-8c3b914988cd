import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Grid,
  GridItem,
  Heading,
  useDisclosure,
  Spinner,
  Center,
  useToast
} from '@chakra-ui/react';
import { useParams } from 'react-router-dom';
import { getMsmeById } from '../api/msme';
import { getNudges } from '../api/nudges';
import NudgeHistory from '../components/NudgeHistory';
import SendNudgeModal from '../components/SendNudgeModal';

// Import existing components
// ...

const MsmeDetailPage = () => {
  const { msmeId } = useParams();
  const [msme, setMsme] = useState(null);
  const [nudges, setNudges] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isNudgesLoading, setIsNudgesLoading] = useState(true);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  // Fetch MSME data
  useEffect(() => {
    const fetchMsmeData = async () => {
      try {
        setIsLoading(true);
        const data = await getMsmeById(msmeId);
        setMsme(data);
      } catch (error) {
        toast({
          title: 'Error fetching MSME data',
          description: error.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (msmeId) {
      fetchMsmeData();
    }
  }, [msmeId, toast]);

  // Fetch Nudges
  const fetchNudges = async () => {
    try {
      setIsNudgesLoading(true);
      const data = await getNudges(msmeId);
      setNudges(data);
    } catch (error) {
      toast({
        title: 'Error fetching nudges',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsNudgesLoading(false);
    }
  };

  useEffect(() => {
    if (msmeId) {
      fetchNudges();
    }
  }, [msmeId]);

  if (isLoading) {
    return (
      <Center h="50vh">
        <Spinner size="xl" />
      </Center>
    );
  }

  if (!msme) {
    return <Box>MSME not found</Box>;
  }

  return (
    <Box p={5}>
      <Grid templateColumns="repeat(12, 1fr)" gap={6}>
        {/* Existing MSME detail components */}
        {/* ... */}
        
        {/* Header with Send Nudge button */}
        <GridItem colSpan={12} mb={4}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Heading size="lg">{msme.business_name}</Heading>
            <Button colorScheme="blue" onClick={onOpen}>
              Send Nudge
            </Button>
          </Box>
        </GridItem>
        
        {/* Main content grid */}
        <GridItem colSpan={{ base: 12, md: 8 }}>
          {/* Existing components like score card, signals, etc. */}
          {/* ... */}
        </GridItem>
        
        {/* Sidebar with Nudge History */}
        <GridItem colSpan={{ base: 12, md: 4 }}>
          {isNudgesLoading ? (
            <Center p={8}>
              <Spinner />
            </Center>
          ) : (
            <NudgeHistory nudges={nudges} />
          )}
        </GridItem>
      </Grid>
      
      {/* Send Nudge Modal */}
      <SendNudgeModal 
        isOpen={isOpen} 
        onClose={onClose} 
        msmeId={msmeId} 
        onNudgeSent={fetchNudges}
      />
    </Box>
  );
};

export default MsmeDetailPage;