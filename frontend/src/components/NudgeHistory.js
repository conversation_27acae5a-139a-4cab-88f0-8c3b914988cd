import React from 'react';
import {
  <PERSON>,
  Text,
  Badge,
  VStack,
  HStack,
  Divider,
  Heading,
  useColorModeValue
} from '@chakra-ui/react';
import { format } from 'date-fns';

// Helper function to get badge color based on medium
const getMediumColor = (medium) => {
  switch (medium) {
    case 'whatsapp':
      return 'green';
    case 'email':
      return 'blue';
    case 'sms':
      return 'orange';
    case 'push_notification':
      return 'purple';
    default:
      return 'gray';
  }
};

// Helper function to get status color
const getStatusColor = (status) => {
  switch (status) {
    case 'delivered':
      return 'green';
    case 'sent':
      return 'blue';
    case 'pending':
      return 'yellow';
    case 'failed':
      return 'red';
    default:
      return 'gray';
  }
};

const NudgeHistory = ({ nudges }) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  if (!nudges || nudges.length === 0) {
    return (
      <Box p={4} borderWidth="1px" borderRadius="lg" bg={bgColor} borderColor={borderColor}>
        <Heading size="md" mb={4}>Nudge History</Heading>
        <Text>No nudges have been sent to this MSME yet.</Text>
      </Box>
    );
  }

  return (
    <Box p={4} borderWidth="1px" borderRadius="lg" bg={bgColor} borderColor={borderColor}>
      <Heading size="md" mb={4}>Nudge History</Heading>
      <VStack spacing={4} align="stretch" divider={<Divider />}>
        {nudges.map((nudge) => (
          <Box key={nudge.nudge_id}>
            <HStack justify="space-between" mb={2}>
              <Text fontSize="sm" color="gray.500">
                {nudge.sent_at ? format(new Date(nudge.sent_at), 'PPp') : 'Date unknown'}
              </Text>
              <HStack>
                <Badge colorScheme={getMediumColor(nudge.medium)}>
                  {nudge.medium}
                </Badge>
                <Badge colorScheme={getStatusColor(nudge.status)}>
                  {nudge.status}
                </Badge>
              </HStack>
            </HStack>
            <Text fontWeight="medium">{nudge.message}</Text>
            <Text fontSize="sm" color="gray.500" mt={1}>
              Trigger: {nudge.trigger_type}
            </Text>
          </Box>
        ))}
      </VStack>
    </Box>
  );
};

export default NudgeHistory;