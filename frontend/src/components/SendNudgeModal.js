import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Textarea,
  Select,
  useToast,
  VStack
} from '@chakra-ui/react';
import { sendNudge } from '../api/nudges';

const SendNudgeModal = ({ isOpen, onClose, msmeId, onNudgeSent }) => {
  const [message, setMessage] = useState('');
  const [medium, setMedium] = useState('email');
  const [triggerType, setTriggerType] = useState('manual');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const toast = useToast();

  const validateForm = () => {
    const newErrors = {};
    if (!message.trim()) {
      newErrors.message = 'Message is required';
    } else if (message.length > 1000) {
      newErrors.message = 'Message must be less than 1000 characters';
    }
    
    if (!medium) {
      newErrors.medium = 'Medium is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setIsLoading(true);
    try {
      const nudgeData = {
        msme_id: msmeId,
        message,
        medium,
        trigger_type: triggerType,
        metadata: { source: 'manual_ui' }
      };
      
      await sendNudge(nudgeData);
      
      toast({
        title: 'Nudge sent successfully',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      
      // Reset form
      setMessage('');
      setMedium('email');
      setTriggerType('manual');
      
      // Close modal and refresh nudges
      onClose();
      if (onNudgeSent) onNudgeSent();
      
    } catch (error) {
      toast({
        title: 'Failed to send nudge',
        description: error.message || 'An unexpected error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Send Nudge</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4}>
            <FormControl isInvalid={errors.message}>
              <FormLabel>Message</FormLabel>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Enter your message to the MSME"
                size="md"
                rows={5}
              />
              <FormErrorMessage>{errors.message}</FormErrorMessage>
            </FormControl>
            
            <FormControl isInvalid={errors.medium}>
              <FormLabel>Delivery Medium</FormLabel>
              <Select value={medium} onChange={(e) => setMedium(e.target.value)}>
                <option value="email">Email</option>
                <option value="whatsapp">WhatsApp</option>
                <option value="sms">SMS</option>
                <option value="push_notification">Push Notification</option>
              </Select>
              <FormErrorMessage>{errors.medium}</FormErrorMessage>
            </FormControl>
            
            <FormControl>
              <FormLabel>Trigger Type</FormLabel>
              <Select value={triggerType} onChange={(e) => setTriggerType(e.target.value)}>
                <option value="manual">Manual</option>
                <option value="score_drop">Score Drop</option>
                <option value="score_improvement">Score Improvement</option>
                <option value="new_signal">New Signal</option>
                <option value="periodic_update">Periodic Update</option>
                <option value="risk_band_change">Risk Band Change</option>
              </Select>
            </FormControl>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            Cancel
          </Button>
          <Button 
            colorScheme="blue" 
            onClick={handleSubmit} 
            isLoading={isLoading}
            loadingText="Sending"
          >
            Send Nudge
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default SendNudgeModal;