import { MSME, Analytics, ScoreDetails, Signal, SignalInput } from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

class ApiError extends Error {
  constructor(message: string, public status: number) {
    super(message);
    this.name = 'ApiError';
  }
}

async function fetchApi<T>(endpoint: string, options?: {
  method?: string;
  body?: any;
}): Promise<T> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: options?.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      ...(options?.body && { body: JSON.stringify(options.body) }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new ApiError(`HTTP error! status: ${response.status} - ${errorText}`, response.status);
    }

    return await response.json();
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`, 0);
  }
}

export const api = {
  // Dashboard Analytics
  async getAnalytics(): Promise<Analytics> {
    return fetchApi<Analytics>('/dashboard/analytics');
  },

  // Portfolio Management
  async getPortfolio(): Promise<MSME[]> {
    return fetchApi<MSME[]>('/dashboard/portfolio');
  },

  // MSME Management
  async getMSME(id: string): Promise<MSME> {
    return fetchApi<MSME>(`/msme/${id}`);
  },

  async getMSMEScore(id: string): Promise<ScoreDetails> {
    return fetchApi<ScoreDetails>(`/msme/${id}/score`);
  },

  // Signal Management
  async getMSMESignals(id: string, limit?: number): Promise<Signal[]> {
    const endpoint = `/msme/${id}/signals${limit ? `?limit=${limit}` : ''}`;
    return fetchApi<Signal[]>(endpoint);
  },

  async addSignalToMSME(msmeId: string, signalInput: SignalInput): Promise<{
    signal_id: string;
    msme_id: string;
    signal_data: Signal;
    score_update: any;
  }> {
    return fetchApi(`/msme/${msmeId}/signals`, {
      method: 'POST',
      body: signalInput,
    });
  },

  // Health check
  async healthCheck(): Promise<{ status: string; service: string }> {
    return fetchApi<{ status: string; service: string }>('/health');
  }
};
