import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

export const sendNudge = async (nudgeData) => {
  try {
    const response = await axios.post(`${API_URL}/nudges/send`, nudgeData);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.detail || 'Failed to send nudge');
  }
};

export const getNudges = async (msmeId) => {
  try {
    const response = await axios.get(`${API_URL}/nudges/${msmeId}`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.detail || 'Failed to fetch nudges');
  }
};